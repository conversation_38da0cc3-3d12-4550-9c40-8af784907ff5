import axios from 'axios';

const ERPNEXT_API_URL = 'https://citysceneglobal.com';
const ERPNEXT_API_KEY = '43bf09851d9bf10';
const ERPNEXT_API_SECRET = '51b781bc020a670';
const AUTH_TOKEN = `token ${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`;

export type User = {
  id: string;
  username: string;
  role: string;
  email?: string;
  isAdmin?: boolean;
  token?: string;
};

// Store user data in localStorage
const storeUserData = (userData: User) => {
  localStorage.setItem('user', JSON.stringify(userData));
};

// Get user data from localStorage
export const getUserData = (): User | null => {
  const userData = localStorage.getItem('user');
  return userData ? JSON.parse(userData) : null;
};

// Clear user data from localStorage
const clearUserData = () => {
  localStorage.removeItem('user');
};

export async function login(username: string, password: string): Promise<User> {
  try {
    console.log(`Authenticating with ERPNext at ${ERPNEXT_API_URL}`);
    
    // Step 1: Login to ERPNext
    const formData = new URLSearchParams();
    formData.append('usr', username);
    formData.append('pwd', password);
    console.log("Hello");
    const loginResponse = await axios.post(`${ERPNEXT_API_URL}/api/method/login`, formData, {
      // withCredentials: true,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }
    });
    console.log("Hello");
    console.log("ERPNext login response:", loginResponse.data);
    
    if (loginResponse.data.message !== "Logged In") {
      throw new Error('Login failed');
    }
    
    // Step 2: Get user details
    const userResponse = await axios.get(`${ERPNEXT_API_URL}/api/resource/User/${username}`, {
      headers: {
        'Authorization': AUTH_TOKEN
      }
    });
    
    console.log("ERPNext user details:", userResponse.data);
    
    const userData = userResponse.data.data;
    const user: User = {
      id: userData.name,
      username: userData.name,
      email: userData.email,
      role: userData.role_profile_name?.toLowerCase() || "employee",
      isAdmin: isAdminUser(userData.email || ''),
      token: AUTH_TOKEN
    };
    
    // Store user data in localStorage
    storeUserData(user);
    
    return user;
  } catch (error) {
    console.error("ERPNext authentication error:", error);
    throw new Error('Login failed');
  }
}

export async function logout(): Promise<void> {
  try {
    await axios.get(`${ERPNEXT_API_URL}/api/method/logout`);
  } catch (error) {
    console.error("Logout error:", error);
  } finally {
    clearUserData();
  }
}

export async function getCurrentUser(): Promise<User | null> {
  const userData = getUserData();
  if (!userData) return null;
  
  try {
    // Verify the user is still valid by making a test request
    await axios.get(`${ERPNEXT_API_URL}/api/resource/User/${userData.username}`, {
      headers: {
        'Authorization': AUTH_TOKEN
      }
    });
    
    return userData;
  } catch (error) {
    clearUserData();
    return null;
  }
}

export function isAdminUser(email: string): boolean {
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  return adminEmails.includes(email);
}

export function hasPermission(user: User | null, requiredRoles: string[]): boolean {
  if (!user) return false;
  if (user.isAdmin) return true;
  return requiredRoles.includes(user.role);
}

