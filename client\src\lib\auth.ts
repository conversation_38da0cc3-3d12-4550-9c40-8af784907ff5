import axios from 'axios';

export type User = {
  id: string;
  username: string;
  role: string;
  email?: string;
  isAdmin?: boolean;
  token?: string;
};

// Store user data in localStorage
const storeUserData = (userData: User) => {
  localStorage.setItem('user', JSON.stringify(userData));
};

// Get user data from localStorage
export const getUserData = (): User | null => {
  const userData = localStorage.getItem('user');
  return userData ? JSON.parse(userData) : null;
};

// Clear user data from localStorage
const clearUserData = () => {
  localStorage.removeItem('user');
};

export async function login(username: string, password: string): Promise<User> {
  try {
    console.log(`Attempting login for user: ${username}`);

    // Call our server's login endpoint instead of ERPNext directly
    const response = await axios.post('/api/login', {
      username,
      password
    }, {
      headers: {
        'Content-Type': 'application/json',
      }
    });

    console.log("Server login response:", response.data);

    if (response.data.message !== "Login successful") {
      throw new Error('Login failed');
    }

    const user: User = {
      id: response.data.user.id,
      username: response.data.user.username,
      email: response.data.user.email,
      role: response.data.user.role,
      isAdmin: response.data.user.isAdmin
    };

    // Store user data in localStorage
    storeUserData(user);

    console.log("Login successful, user data stored:", user);

    return user;
  } catch (error) {
    console.error("Login authentication error:", error);
    throw new Error('Login failed');
  }
}

export async function logout(): Promise<void> {
  try {
    await axios.post('/api/logout');
    console.log("Logout successful");
  } catch (error) {
    console.error("Logout error:", error);
  } finally {
    clearUserData();
  }
}

export async function getCurrentUser(): Promise<User | null> {
  const userData = getUserData();
  if (!userData) return null;

  try {
    // Verify the user is still valid by calling our server's /api/me endpoint
    const response = await axios.get('/api/me');

    const user: User = {
      id: response.data.id,
      username: response.data.username,
      email: response.data.email,
      role: response.data.role,
      isAdmin: response.data.isAdmin
    };

    // Update stored user data with fresh data from server
    storeUserData(user);

    return user;
  } catch (error) {
    console.error("Failed to get current user:", error);
    clearUserData();
    return null;
  }
}

export function isAdminUser(email: string): boolean {
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  return adminEmails.includes(email);
}

export function hasPermission(user: User | null, requiredRoles: string[]): boolean {
  if (!user) return false;
  if (user.isAdmin) return true;
  return requiredRoles.includes(user.role);
}

