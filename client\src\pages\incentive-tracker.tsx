import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Calculator, User, TrendingUp, DollarSign, Award, Plus, Info, FileText, Trash2, LogOut, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from '@/lib/queryClient';
import { useLocation } from 'wouter';
import { logout } from '@/lib/auth';
import type { InsertProject } from '@shared/schema';

interface Employee {
  id: string;
  name: string;
  email?: string;
  incentiveRate: string;
}

interface Project {
  id: string;
  projectName: string;
  expectedCost: number;
  saleValue: number;
  profit: number;
  employeeId: string;
  date: string;
  buyerName?: string;
  location?: string;
}

interface IncentiveResult {
  eligible: boolean;
  incentive: number;
  calculated: number;
  capped?: boolean;
  maxCap?: number;
  eligibilityThreshold: number;
  annualSalary: number;
  reason: string;
  case: string;
  caseNumber: number;
}

interface IncentiveTrackerProps {
  user: {
    id: number;
    username: string;
    role: string;
    email?: string;
    isAdmin?: boolean;
  };
}

export default function IncentiveTracker({ user }: IncentiveTrackerProps) {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedEmployee, setSelectedEmployee] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState<string>('month');
  const [newProject, setNewProject] = useState<Partial<InsertProject>>({
    projectName: '',
    expectedCost: 0,
    saleValue: 0,
    employeeId: '',
    userId: user.id,
    buyerName: '',
    location: ''
  });
  
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();

  // Queries
  const { data: employees = [], isLoading: isLoadingEmployees } = useQuery<Employee[]>({
    queryKey: ['/api/employees'],
  });

  const { data: projects = [], isLoading: isLoadingProjects, refetch: refetchProjects } = useQuery<Project[]>({
    queryKey: ['/api/projects', { period: selectedPeriod }],
  });

  const { data: employeeProjects = [], isLoading: isLoadingEmployeeProjects, refetch: refetchEmployeeProjects } = useQuery<Project[]>({
    queryKey: ['/api/projects/employee', selectedEmployee],
    enabled: !!selectedEmployee,
  });

  // Set default employee if user is not admin
  useEffect(() => {
    if (!user.isAdmin && employees.length > 0) {
      // Find employee that matches current user
      const userEmployee = employees.find(emp => emp.email === user.email);
      if (userEmployee) {
        setSelectedEmployee(userEmployee.id);
        setNewProject(prev => ({ ...prev, employeeId: userEmployee.id }));
      }
    }
  }, [employees, user]);

  // Mutations
  const addProjectMutation = useMutation({
    mutationFn: async (project: InsertProject) => {
      const response = await apiRequest('POST', '/api/projects', project);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/projects'] });
      queryClient.invalidateQueries({ queryKey: ['/api/projects/employee'] });
      setNewProject({ 
        projectName: '', 
        expectedCost: 0, 
        saleValue: 0, 
        employeeId: user.isAdmin ? '' : newProject.employeeId,
        userId: user.id,
        buyerName: '',
        location: ''
      });
      toast({ title: 'Project added successfully!' });
    },
    onError: () => {
      toast({ title: 'Failed to add project', variant: 'destructive' });
    }
  });

  // Utility functions
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(value);
  };

  const calculateIncentive = (employee: Employee, totalProfit: number): IncentiveResult => {
    const incentiveRate = parseFloat(employee.incentiveRate) / 100;
    const annualSalary = 45000 * 12; // Default annual salary
    const eligibilityThreshold = annualSalary * 0.5;
    
    let caseNumber = 0;
    let caseDescription = '';
    
    if (totalProfit < eligibilityThreshold) {
      caseNumber = 1;
      caseDescription = 'Below Threshold';
      return {
        eligible: false,
        incentive: 0,
        calculated: 0,
        eligibilityThreshold,
        annualSalary,
        reason: `Total profit (${formatCurrency(totalProfit)}) is below 50% of annual salary (${formatCurrency(eligibilityThreshold)})`,
        case: caseDescription,
        caseNumber
      };
    }
    
    let calculatedIncentive = totalProfit * incentiveRate;
    let finalIncentive = calculatedIncentive;
    let isCapped = false;
    let maxCap = 0;
    
    if (totalProfit >= eligibilityThreshold && totalProfit <= annualSalary) {
      caseNumber = 2;
      caseDescription = 'Standard Rate';
    } else if (totalProfit > annualSalary && totalProfit <= annualSalary * 2) {
      caseNumber = 3;
      caseDescription = 'Exceeds Annual Salary';
    } else {
      caseNumber = 4;
      caseDescription = 'Exceeds 2x Annual Salary';
      maxCap = annualSalary * 0.3;
      
      if (calculatedIncentive > maxCap) {
        finalIncentive = maxCap;
        isCapped = true;
      }
    }
    
    return {
      eligible: true,
      incentive: finalIncentive,
      calculated: calculatedIncentive,
      capped: isCapped,
      maxCap: maxCap,
      eligibilityThreshold: eligibilityThreshold,
      annualSalary: annualSalary,
      reason: 'Eligible for incentive',
      case: caseDescription,
      caseNumber
    };
  };

  const getCaseColor = (caseNumber: number) => {
    switch(caseNumber) {
      case 1: return 'bg-red-100 text-red-800 border-red-200';
      case 2: return 'bg-green-100 text-green-800 border-green-200';
      case 3: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 4: return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Handlers
  const handleAddProject = () => {
    if (!newProject.projectName || !newProject.expectedCost || !newProject.saleValue || !newProject.employeeId) {
      toast({ title: 'Please fill in all required fields', variant: 'destructive' });
      return;
    }
    
    addProjectMutation.mutate(newProject as InsertProject);
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      toast({ title: 'Logout failed', variant: 'destructive' });
    }
  };

  const handleRefresh = () => {
    refetchProjects();
    if (selectedEmployee) {
      refetchEmployeeProjects();
    }
  };

  // Dashboard calculations
  const totalProjects = projects.length;
  const totalSales = projects.reduce((sum, project) => sum + project.saleValue, 0);
  const totalProfit = projects.reduce((sum, project) => sum + project.profit, 0);
  
  // Employee-specific calculations
  const employeeProjectsData = selectedEmployee ? employeeProjects : [];
  const employeeProjectsCount = employeeProjectsData.length;
  const employeeProjectsSales = employeeProjectsData.reduce((sum, project) => sum + project.saleValue, 0);
  const employeeProjectsProfit = employeeProjectsData.reduce((sum, project) => sum + project.profit, 0);
  
  const selectedEmployeeData = employees.find(emp => emp.id === selectedEmployee);
  const incentiveResult = selectedEmployeeData 
    ? calculateIncentive(selectedEmployeeData, employeeProjectsProfit)
    : null;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-800 mb-2">Monthly Employee Incentive Tracker</h1>
                <p className="text-gray-600">Track monthly employee performance and calculate incentives based on project profits and established thresholds</p>
                <div className="flex items-center mt-2 text-sm text-blue-600">
                  <FileText className="w-4 h-4 mr-1" />
                  <span>Based on Company Incentive Policy Document</span>
                </div>
              </div>
              <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-3">
                <Award className="w-8 h-8 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Navigation */}
        <Card className="mb-6">
          <div className="flex border-b overflow-x-auto">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: Calculator },
              { id: 'employees', label: 'Manage Employees', icon: User },
              { id: 'projects', label: 'Add Projects', icon: TrendingUp },
              { id: 'rules', label: 'Incentive Rules', icon: DollarSign }
            ].map(tab => {
              const Icon = tab.icon;
              return (
                <Button
                  key={tab.id}
                  variant="ghost"
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-6 py-3 transition-colors whitespace-nowrap rounded-none ${
                    activeTab === tab.id 
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' 
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.label}
                </Button>
              );
            })}
          </div>
        </Card>

        {/* Main Content */}
        <Card>
          <CardContent className="p-6">
            {activeTab === 'dashboard' && (
              <div className="space-y-6">
                {/* Employee Selection */}
                <div>
                  <Label className="text-lg font-semibold text-gray-700 mb-3">Select Employee for Monthly Analysis</Label>
                  <Select value={selectedEmployee} onValueChange={setSelectedEmployee}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select an employee to view incentive details" />
                    </SelectTrigger>
                    <SelectContent>
                      {employees.map(emp => (
                        <SelectItem key={emp.id} value={emp.id.toString()}>{emp.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Results */}
                {selectedEmp && incentiveResult && (
                  <div className="space-y-6">
                    {/* Employee Header Card */}
                    <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
                      <div className="flex justify-between items-start">
                        <div>
                          <h2 className="text-2xl font-bold mb-2">{selectedEmp.name}</h2>
                          <p className="text-blue-100 mb-1">Monthly Salary: {formatCurrency(selectedEmp.salary)}</p>
                          <p className="text-blue-100 mb-1">Incentive Rate: {selectedEmp.incentiveRate}% (Custom Rate)</p>
                          <p className="text-blue-100">Analysis Period: December 2024</p>
                        </div>
                        <div className="text-right">
                          <Badge className={`mb-2 ${
                            incentiveResult.eligible 
                              ? 'bg-green-500 text-white' 
                              : 'bg-red-500 text-white'
                          }`}>
                            {incentiveResult.eligible ? 'Eligible for Incentive' : 'Not Eligible'}
                          </Badge>
                          <div className={`px-3 py-1 rounded-full text-xs font-medium border-2 ${getCaseColor(incentiveResult.caseNumber)}`}>
                            {incentiveResult.case.split(':')[0]}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Key Metrics Cards */}
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <Card className="bg-blue-50 border-blue-200">
                        <CardContent className="p-4 text-center">
                          <div className="text-2xl font-bold text-blue-600">{employeeProjects.length}</div>
                          <div className="text-sm text-gray-600">Projects Completed</div>
                        </CardContent>
                      </Card>
                      <Card className={`${monthlyProfit >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                        <CardContent className="p-4 text-center">
                          <div className={`text-2xl font-bold ${monthlyProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {formatCurrency(monthlyProfit)}
                          </div>
                          <div className="text-sm text-gray-600">
                            {monthlyProfit >= 0 ? 'Cumulative Monthly Profit' : 'Cumulative Monthly Loss'}
                          </div>
                        </CardContent>
                      </Card>
                      <Card className="bg-yellow-50 border-yellow-200">
                        <CardContent className="p-4 text-center">
                          <div className="text-2xl font-bold text-yellow-600">{formatCurrency(2 * selectedEmp.salary)}</div>
                          <div className="text-sm text-gray-600">Eligibility Threshold (2× Salary)</div>
                        </CardContent>
                      </Card>
                      <Card className="bg-purple-50 border-purple-200">
                        <CardContent className="p-4 text-center">
                          <div className="text-2xl font-bold text-purple-600">{formatCurrency(incentiveResult.incentive)}</div>
                          <div className="text-sm text-gray-600">Monthly Incentive</div>
                        </CardContent>
                      </Card>
                    </div>

                    {/* Calculation Details */}
                    <Card className="bg-gray-50">
                      <CardHeader>
                        <CardTitle className="flex items-center">
                          <Info className="mr-2 text-blue-500" />
                          Incentive Calculation Details
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className={`p-4 rounded border-l-4 ${incentiveResult.eligible ? 'border-green-500' : 'border-red-500'}`}>
                          <h4 className="font-semibold text-gray-800 mb-2">{incentiveResult.case}</h4>
                          <p className="text-gray-600 mb-3">{incentiveResult.reason}</p>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span>{monthlyProfit >= 0 ? 'Total Monthly Profit:' : 'Total Monthly Loss:'}</span>
                              <span className={`font-semibold ${monthlyProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {formatCurrency(monthlyProfit)}
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span>Eligibility Threshold (2× Salary):</span>
                              <span className="font-semibold">{formatCurrency(incentiveResult.eligibilityThreshold)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span>Incentive Rate:</span>
                              <span className="font-semibold">{selectedEmp.incentiveRate}%</span>
                            </div>
                          </div>
                          {incentiveResult.eligible && (
                            <div className="space-y-2 text-sm">
                              <div className="flex justify-between">
                                <span>Total Monthly Profit:</span>
                                <span className="font-semibold">{formatCurrency(monthlyProfit)}</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Incentive Rate:</span>
                                <span className="font-semibold">{selectedEmp.incentiveRate}%</span>
                              </div>
                              <div className="flex justify-between">
                                <span>Calculation:</span>
                                <span className="font-semibold">
                                  {incentiveResult.caseNumber === 4 
                                    ? `${formatCurrency(monthlyProfit)} × ${selectedEmp.incentiveRate}% = ${formatCurrency(incentiveResult.calculated)}`
                                    : `(${formatCurrency(monthlyProfit)} - ${formatCurrency(selectedEmp.salary)}) × ${selectedEmp.incentiveRate}% = ${formatCurrency(incentiveResult.calculated)}`
                                  }
                                </span>
                              </div>
                              <div className="flex justify-between">
                                <span>Maximum Cap:</span>
                                <span className="font-semibold">{formatCurrency(incentiveResult.maxCap!)}</span>
                              </div>
                              <div className="flex justify-between border-t pt-2">
                                <span className="font-semibold">Final Incentive:</span>
                                <span className="font-bold text-green-600">{formatCurrency(incentiveResult.incentive)}</span>
                              </div>
                              {incentiveResult.capped && (
                                <p className="text-orange-600 text-xs mt-2">
                                  <Info className="w-3 h-3 inline mr-1" />
                                  Incentive capped at maximum limit
                                </p>
                              )}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Projects List */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-800 mb-4">Projects Completed This Month</h3>
                      <div className="space-y-3">
                        {employeeProjects.length === 0 ? (
                          <p className="text-gray-500 text-center py-8">No projects found for this employee</p>
                        ) : (
                          employeeProjects.map(proj => (
                            <Card key={proj.id} className={`${proj.profit >= 0 ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
                              <CardContent className="p-4">
                                <div className="flex justify-between items-start">
                                  <h4 className="font-semibold text-gray-800">{proj.projectName}</h4>
                                  <Badge className={`${proj.profit >= 0 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                                    {proj.profit >= 0 ? 'Profit' : 'Loss'}
                                  </Badge>
                                </div>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm mt-2">
                                  <div><span className="text-gray-500">Expected Cost:</span> {formatCurrency(proj.expectedCost)}</div>
                                  <div><span className="text-gray-500">Sale Value:</span> {formatCurrency(proj.saleValue)}</div>
                                  <div>
                                    <span className="text-gray-500">{proj.profit >= 0 ? 'Profit:' : 'Loss:'}</span> 
                                    <span className={`font-semibold ml-1 ${proj.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                      {proj.profit >= 0 ? formatCurrency(proj.profit) : formatCurrency(Math.abs(proj.profit))}
                                    </span>
                                  </div>
                                  <div><span className="text-gray-500">Date:</span> {new Date(proj.date!).toLocaleDateString()}</div>
                                </div>
                              </CardContent>
                            </Card>
                          ))
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Empty State */}
                {employees.length === 0 && (
                  <div className="text-center py-12">
                    <User className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                    <h3 className="text-xl font-semibold text-gray-600 mb-2">No Employees Added Yet</h3>
                    <p className="text-gray-500 mb-6">Start by adding employees to track their incentives</p>
                    <Button onClick={() => setActiveTab('employees')}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Your First Employee
                    </Button>
                  </div>
                )}
              </div>
            )}

            {activeTab === 'employees' && (
              <div className="space-y-6">
                {/* Add Employee Form */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Plus className="mr-2 text-blue-500" />
                      Add New Employee
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="employeeName">Employee Name</Label>
                        <Input
                          id="employeeName"
                          placeholder="Enter full name"
                          value={newEmployee.name}
                          onChange={(e) => setNewEmployee({...newEmployee, name: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="employeeSalary">Monthly Salary (₹)</Label>
                        <Input
                          id="employeeSalary"
                          type="number"
                          placeholder="45000"
                          value={newEmployee.salary || ''}
                          onChange={(e) => setNewEmployee({...newEmployee, salary: parseInt(e.target.value) || 0})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="employeeRate">Incentive Rate (%)</Label>
                        <Input
                          id="employeeRate"
                          type="number"
                          min="1"
                          max="25"
                          placeholder="15"
                          value={newEmployee.incentiveRate}
                          onChange={(e) => setNewEmployee({...newEmployee, incentiveRate: e.target.value})}
                        />
                      </div>
                    </div>
                    <Button className="mt-4" onClick={handleAddEmployee} disabled={addEmployeeMutation.isPending}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Employee
                    </Button>
                  </CardContent>
                </Card>

                {/* Employees List */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Current Employees</h3>
                  <div className="space-y-3">
                    {employees.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">No employees added yet</p>
                    ) : (
                      employees.map(emp => (
                        <Card key={emp.id}>
                          <CardContent className="p-4 flex justify-between items-center">
                            <div>
                              <h4 className="font-semibold text-gray-800">{emp.name}</h4>
                              <p className="text-sm text-gray-600">
                                Salary: {formatCurrency(emp.salary)} | Incentive Rate: {emp.incentiveRate}%
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteEmployeeMutation.mutate(emp.id)}
                              disabled={deleteEmployeeMutation.isPending}
                              className="text-red-600 hover:text-red-800"
                            >
                              <Trash2 className="w-4 h-4" />
                            </Button>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'projects' && (
              <div className="space-y-6">
                {/* Add Project Form */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Plus className="mr-2 text-blue-500" />
                      Add New Project
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="projectName">Project Name</Label>
                        <Input
                          id="projectName"
                          placeholder="Enter project name"
                          value={newProject.projectName}
                          onChange={(e) => setNewProject({...newProject, projectName: e.target.value})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="projectEmployee">Assign to Employee</Label>
                        <Select 
                          value={newProject.employeeId.toString()} 
                          onValueChange={(value) => setNewProject({...newProject, employeeId: parseInt(value)})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select employee" />
                          </SelectTrigger>
                          <SelectContent>
                            {employees.map(emp => (
                              <SelectItem key={emp.id} value={emp.id.toString()}>{emp.name}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="projectSale">Sale Value (₹)</Label>
                        <Input
                          id="projectSale"
                          type="number"
                          placeholder="300000"
                          value={newProject.saleValue || ''}
                          onChange={(e) => setNewProject({...newProject, saleValue: parseInt(e.target.value) || 0})}
                        />
                      </div>
                      <div>
                        <Label htmlFor="projectCost">Expected Cost (₹)</Label>
                        <Input
                          id="projectCost"
                          type="number"
                          placeholder="270000"
                          value={newProject.expectedCost || ''}
                          onChange={(e) => setNewProject({...newProject, expectedCost: parseInt(e.target.value) || 0})}
                        />
                      </div>
                    </div>
                    {(newProject.saleValue > 0 || newProject.expectedCost > 0) && (
                      <Card className={`mt-4 ${(newProject.saleValue - newProject.expectedCost) >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
                        <CardContent className="p-4">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-700">
                              {(newProject.saleValue - newProject.expectedCost) >= 0 ? 'Calculated Profit:' : 'Calculated Loss:'}
                            </span>
                            <span className={`text-lg font-bold ${(newProject.saleValue - newProject.expectedCost) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {formatCurrency(Math.abs(newProject.saleValue - newProject.expectedCost))}
                            </span>
                          </div>
                        </CardContent>
                      </Card>
                    )}
                    <Button className="mt-4" onClick={handleAddProject} disabled={addProjectMutation.isPending}>
                      <Plus className="w-4 h-4 mr-2" />
                      Add Project
                    </Button>
                  </CardContent>
                </Card>

                {/* Projects List */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">All Projects</h3>
                  <div className="space-y-3">
                    {projects.length === 0 ? (
                      <p className="text-gray-500 text-center py-8">No projects added yet</p>
                    ) : (
                      projects.map(proj => {
                        const employee = employees.find(emp => emp.id === proj.employeeId);
                        return (
                          <Card key={proj.id}>
                            <CardContent className="p-4">
                              <div className="flex justify-between items-start">
                                <div className="flex-1">
                                  <h4 className="font-semibold text-gray-800">{proj.projectName}</h4>
                                  <p className="text-sm text-gray-600 mb-2">Employee: {employee?.name || 'Unknown'}</p>
                                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                                    <div><span className="text-gray-500">Cost:</span> {formatCurrency(proj.expectedCost)}</div>
                                    <div><span className="text-gray-500">Sale:</span> {formatCurrency(proj.saleValue)}</div>
                                    <div>
                                      <span className="text-gray-500">{proj.profit >= 0 ? 'Profit:' : 'Loss:'}</span> 
                                      <span className={`font-semibold ml-1 ${proj.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                        {proj.profit >= 0 ? formatCurrency(proj.profit) : formatCurrency(Math.abs(proj.profit))}
                                      </span>
                                    </div>
                                    <div><span className="text-gray-500">Date:</span> {new Date(proj.date!).toLocaleDateString()}</div>
                                  </div>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => deleteProjectMutation.mutate(proj.id)}
                                  disabled={deleteProjectMutation.isPending}
                                  className="text-red-600 hover:text-red-800 ml-4"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        );
                      })
                    )}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'rules' && (
              <div className="space-y-6">
                <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
                  <h2 className="text-2xl font-bold mb-2">Incentive Calculation Rules</h2>
                  <p className="text-blue-100">Based on Company Policy Document</p>
                </div>

                {/* Rules Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card className="border-red-200 bg-red-50">
                    <CardContent className="p-6">
                      <div className="flex items-center mb-3">
                        <div className="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">1</div>
                        <h3 className="text-lg font-semibold text-red-800">Not Eligible</h3>
                      </div>
                      <p className="text-red-700 mb-3">Cumulative monthly profit &lt; 2× monthly salary</p>
                      <div className="text-sm text-red-600">
                        <strong>Result:</strong> No incentive paid
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-green-200 bg-green-50">
                    <CardContent className="p-6">
                      <div className="flex items-center mb-3">
                        <div className="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">2</div>
                        <h3 className="text-lg font-semibold text-green-800">Normal Calculation</h3>
                      </div>
                      <p className="text-green-700 mb-3">Cumulative monthly profit ≥ 2× monthly salary</p>
                      <div className="text-sm text-green-600">
                        <strong>Formula:</strong> (Gross Profit - Monthly Salary) × Incentive Rate%<br />
                        <strong>Cap:</strong> Monthly salary
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-yellow-200 bg-yellow-50">
                    <CardContent className="p-6">
                      <div className="flex items-center mb-3">
                        <div className="bg-yellow-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">3</div>
                        <h3 className="text-lg font-semibold text-yellow-800">Capping Applied</h3>
                      </div>
                      <p className="text-yellow-700 mb-3">Calculated incentive &gt; monthly salary</p>
                      <div className="text-sm text-yellow-600">
                        <strong>Result:</strong> Incentive capped at monthly salary amount
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="border-purple-200 bg-purple-50">
                    <CardContent className="p-6">
                      <div className="flex items-center mb-3">
                        <div className="bg-purple-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold mr-3">4</div>
                        <h3 className="text-lg font-semibold text-purple-800">Exceptional Performance</h3>
                      </div>
                      <p className="text-purple-700 mb-3">Cumulative monthly profit &gt; annual salary</p>
                      <div className="text-sm text-purple-600">
                        <strong>Formula:</strong> Total Gross Profit × Incentive Rate%<br />
                        <strong>Cap:</strong> 2× monthly salary
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Example Calculation */}
                <Card className="bg-gray-50">
                  <CardHeader>
                    <CardTitle>Example Calculation</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Employee:</strong> John Doe<br />
                        <strong>Monthly Salary:</strong> ₹45,000<br />
                        <strong>Incentive Rate:</strong> 15%<br />
                        <strong>Monthly Profit:</strong> ₹1,20,000
                      </div>
                      <div>
                        <strong>Eligibility:</strong> ₹1,20,000 ≥ ₹90,000 ✓<br />
                        <strong>Calculation:</strong> (₹1,20,000 - ₹45,000) × 15% = ₹11,250<br />
                        <strong>Final Incentive:</strong> ₹11,250 (within cap)
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}





