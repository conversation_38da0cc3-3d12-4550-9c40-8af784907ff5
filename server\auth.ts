import axios from 'axios';
import { Request, Response, NextFunction } from 'express';
import { storage } from './storage';

const ERPNEXT_API_URL = process.env.ERPNEXT_API_URL || 'https://citysceneglobal.com';
const ERPNEXT_API_KEY = process.env.ERPNEXT_API_KEY || '43bf09851d9bf10';
const ERPNEXT_API_SECRET = process.env.ERPNEXT_API_SECRET || '51b781bc020a670';

// Authenticate with ERPNext
export async function authenticateWithERPNext(username: string, password: string) {
  try {
    console.log(`Authenticating with ERPNext at ${ERPNEXT_API_URL}`);
    
    const formData = new URLSearchParams();
    formData.append('usr', username);
    formData.append('pwd', password);
    console.log("Hello2");
    const response = await axios.post(`${ERPNEXT_API_URL}/api/method/login`, formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      }
    });
    console.log("Hello2");
    console.log("ERPNext login response:", response.data);
    
    if (response.data.message === "Logged In") {
      // Get user details from ERPNext
      const userResponse = await axios.get(`${ERPNEXT_API_URL}/api/resource/User/${username}`, {
        headers: {
          'Authorization': `token ${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`
        }
      });
      
      console.log("ERPNext user details:", userResponse.data);
      return userResponse.data.data;
    }
    return null;
  } catch (error) {
    console.error("ERPNext authentication error:", error);
    return null;
  }
}

// Middleware to check if user is authenticated
export function isAuthenticated(req: Request, res: Response, next: NextFunction) {
  if (req.session && req.session.userId) {
    return next();
  }
  res.status(401).json({ message: "Unauthorized" });
}

// Check if user is admin
export function isAdmin(email: string): boolean {
  const adminEmails = ['<EMAIL>', '<EMAIL>'];
  return adminEmails.includes(email);
}

// Middleware to check user role permissions
export function hasRole(roles: string[]) {
  return async (req: Request, res: Response, next: NextFunction) => {
    if (!req.session || !req.session.userId) {
      return res.status(401).json({ message: "Unauthorized" });
    }
    
    const user = await storage.getUser(req.session.userId);
    if (!user) {
      return res.status(401).json({ message: "User not found" });
    }
    
    // Admin users have access to everything
    if (user.email && isAdmin(user.email)) {
      return next();
    }
    
    if (roles.includes(user.role)) {
      return next();
    }
    
    res.status(403).json({ message: "Forbidden: Insufficient permissions" });
  };
}



