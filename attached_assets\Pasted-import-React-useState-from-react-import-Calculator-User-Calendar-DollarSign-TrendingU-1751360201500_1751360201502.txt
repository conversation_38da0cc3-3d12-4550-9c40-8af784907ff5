import React, { useState } from 'react';
import { Calculator, User, Calendar, DollarSign, TrendingUp, Award, Plus, Info, AlertCircle, FileText, Target, Trash2 } from 'lucide-react';

const IncentiveTracker = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedEmployee, setSelectedEmployee] = useState('');
  const [selectedMonth, setSelectedMonth] = useState('July 2025');
  
  const [employees, setEmployees] = useState([
    {
      id: 1,
      name: 'Vedant Dave',
      salary: 45000,
      incentiveRate: 15
    }
  ]);
  
  const [projects, setProjects] = useState([
    {
      id: 1,
      projectName: 'Project Alpha',
      expectedCost: 270000,
      saleValue: 300000,
      profit: 30000,
      employee: '1',
      date: '2025-07-01',
      buyerName: 'ABC Corp',
      location: 'Mumbai'
    },
    {
      id: 2,
      projectName: 'Project Beta',
      expectedCost: 150000,
      saleValue: 190000,
      profit: 40000,
      employee: '1',
      date: '2025-07-15',
      buyerName: 'XYZ Ltd',
      location: 'Delhi'
    }
  ]);
  
  const [newProject, setNewProject] = useState({
    projectName: '',
    expectedCost: '',
    saleValue: '',
    buyerName: '',
    location: ''
  });
  const [newEmployee, setNewEmployee] = useState({
    name: '',
    salary: '',
    incentiveRate: '15'
  });

  const formatNumber = (value) => {
    if (!value) return '';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  };

  const parseNumber = (value) => {
    return parseFloat(value.replace(/,/g, '')) || 0;
  };

  const calculateIncentive = (totalProfit, salary, incentiveRate) => {
    const eligibilityThreshold = 2 * salary; // 2× monthly salary
    const annualSalary = 12 * salary; // Annual salary threshold
    
    // Case 1: Not eligible - profit less than 2× monthly salary
    if (totalProfit < eligibilityThreshold) {
      return {
        eligible: false,
        incentive: 0,
        calculated: 0,
        reason: `Cumulative monthly profit (₹${totalProfit.toLocaleString('en-IN')}) is less than 2× monthly salary (₹${eligibilityThreshold.toLocaleString('en-IN')})`,
        case: 'Case 1: Employee Not Yet Eligible for Incentive',
        caseNumber: 1
      };
    }
    
    let calculatedIncentive = 0;
    let maxCap = salary; // Default cap is monthly salary
    let caseDescription = '';
    let caseNumber = 2;
    
    // Case 4: Exceptional Performance - profit exceeds annual salary
    if (totalProfit > annualSalary) {
      calculatedIncentive = totalProfit * (incentiveRate / 100); // 15% of total gross profit
      maxCap = 2 * salary; // Cap increased to 2× monthly salary
      caseDescription = 'Case 4: Exceptional Performance -- Profit Exceeds Annual Salary';
      caseNumber = 4;
    }
    // Case 2/3: Normal calculation - profit between 2× salary and annual salary
    else {
      calculatedIncentive = (totalProfit - salary) * (incentiveRate / 100); // (Gross Profit - Monthly Salary) × 15%
      maxCap = salary; // Cap is monthly salary
      caseDescription = 'Case 2: Employee Becomes Eligible -- Normal Incentive Calculation';
      caseNumber = 2;
    }
    
    const finalIncentive = Math.min(calculatedIncentive, maxCap);
    const isCapped = calculatedIncentive > maxCap;
    
    // Determine if it's Case 3 (capping applied)
    if (isCapped && totalProfit <= annualSalary) {
      caseDescription = 'Case 3: Incentive is Higher Than Monthly Salary (Capping Applied)';
      caseNumber = 3;
    }
    
    return {
      eligible: true,
      incentive: finalIncentive,
      calculated: calculatedIncentive,
      capped: isCapped,
      maxCap: maxCap,
      eligibilityThreshold: eligibilityThreshold,
      annualSalary: annualSalary,
      reason: 'Eligible for incentive',
      case: caseDescription,
      caseNumber: caseNumber
    };
  };

  const addEmployee = () => {
    if (newEmployee.name && newEmployee.salary) {
      setEmployees([...employees, {
        id: Date.now(),
        name: newEmployee.name,
        salary: parseNumber(newEmployee.salary),
        incentiveRate: 15 // Fixed at 15%
      }]);
      setNewEmployee({ name: '', salary: '', incentiveRate: '15' });
    }
  };

  const addProject = () => {
    if (newProject.projectName && newProject.expectedCost && newProject.saleValue && selectedEmployee) {
      const profit = parseNumber(newProject.saleValue) - parseNumber(newProject.expectedCost);
      setProjects([...projects, {
        id: Date.now(),
        projectName: newProject.projectName,
        expectedCost: parseNumber(newProject.expectedCost),
        saleValue: parseNumber(newProject.saleValue),
        profit: profit,
        employee: selectedEmployee,
        date: new Date().toISOString().split('T')[0],
        buyerName: newProject.buyerName || 'N/A',
        location: newProject.location || 'N/A'
      }]);
      setNewProject({ projectName: '', expectedCost: '', saleValue: '', buyerName: '', location: '' });
    }
  };

  const deleteProject = (projectId) => {
    setProjects(projects.filter(p => p.id !== projectId));
  };

  const deleteEmployee = (employeeId) => {
    setEmployees(employees.filter(e => e.id !== employeeId));
    setProjects(projects.filter(p => p.employee !== employeeId.toString()));
    if (selectedEmployee === employeeId.toString()) {
      setSelectedEmployee('');
    }
  };

  const selectedEmp = employees.find(emp => emp.id === parseInt(selectedEmployee));
  const empProjects = projects.filter(p => p.employee === selectedEmployee);
  const monthlyProfit = empProjects.reduce((sum, p) => sum + p.profit, 0);
  const incentiveResult = selectedEmp ? calculateIncentive(monthlyProfit, selectedEmp.salary, selectedEmp.incentiveRate) : null;

  const handleNumberInput = (value, setter, field) => {
    const numericValue = value.replace(/[^0-9]/g, '');
    setter(prev => ({ ...prev, [field]: formatNumber(numericValue) }));
  };

  const getCaseColor = (caseNumber) => {
    switch(caseNumber) {
      case 1: return 'bg-red-100 text-red-800 border-red-200';
      case 2: return 'bg-green-100 text-green-800 border-green-200';
      case 3: return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 4: return 'bg-purple-100 text-purple-800 border-purple-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800 mb-2">Monthly Employee Incentive Tracker</h1>
              <p className="text-gray-600">Track monthly employee performance and calculate incentives based on project profits and established thresholds</p>
              <div className="flex items-center mt-2 text-sm text-blue-600">
                <FileText className="w-4 h-4 mr-1" />
                <span>Based on Company Incentive Policy Document</span>
              </div>
            </div>
            <div className="bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-3">
              <Award className="w-8 h-8 text-white" />
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="bg-white rounded-lg shadow-lg mb-6">
          <div className="flex border-b overflow-x-auto">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: Calculator },
              { id: 'employees', label: 'Manage Employees', icon: User },
              { id: 'projects', label: 'Add Projects', icon: TrendingUp },
              { id: 'rules', label: 'Incentive Rules', icon: DollarSign }
            ].map(tab => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-6 py-3 transition-colors whitespace-nowrap ${
                    activeTab === tab.id 
                      ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50' 
                      : 'text-gray-600 hover:text-blue-600 hover:bg-gray-50'
                  }`}
                >
                  <Icon className="w-4 h-4 mr-2" />
                  {tab.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Main Content */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Employee Selection */}
              <div>
                <label className="block text-lg font-semibold text-gray-700 mb-3">Select Employee for Monthly Analysis</label>
                <select
                  value={selectedEmployee}
                  onChange={(e) => setSelectedEmployee(e.target.value)}
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                >
                  <option value="">Select an employee to view incentive details</option>
                  {employees.map(emp => (
                    <option key={emp.id} value={emp.id}>{emp.name}</option>
                  ))}
                </select>
              </div>

              {/* Results */}
              {selectedEmp && (
                <div className="space-y-6">
                  {/* Employee Header Card */}
                  <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg p-6">
                    <div className="flex justify-between items-start">
                      <div>
                        <h2 className="text-2xl font-bold mb-2">{selectedEmp.name}</h2>
                        <p className="text-blue-100 mb-1">Monthly Salary: ₹{selectedEmp.salary.toLocaleString('en-IN')}</p>
                        <p className="text-blue-100 mb-1">Incentive Rate: {selectedEmp.incentiveRate}% (Company Policy)</p>
                        <p className="text-blue-100">Analysis Period: {selectedMonth}</p>
                      </div>
                      <div className="text-right">
                        <div className={`px-4 py-2 rounded-full text-sm font-medium mb-2 ${
                          incentiveResult?.eligible 
                            ? 'bg-green-500 text-white' 
                            : 'bg-red-500 text-white'
                        }`}>
                          {incentiveResult?.eligible ? 'Eligible for Incentive' : 'Not Eligible'}
                        </div>
                        <div className={`px-3 py-1 rounded-full text-xs font-medium border-2 ${getCaseColor(incentiveResult?.caseNumber)}`}>
                          {incentiveResult?.case?.split(':')[0]}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Key Metrics Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="bg-blue-50 rounded-lg p-4 text-center hover:bg-blue-100 transition-colors border border-blue-200">
                      <div className="text-2xl font-bold text-blue-600">{empProjects.length}</div>
                      <div className="text-sm text-gray-600">Projects Completed</div>
                    </div>
                    <div className="bg-green-50 rounded-lg p-4 text-center hover:bg-green-100 transition-colors border border-green-200">
                      <div className="text-2xl font-bold text-green-600">₹{monthlyProfit.toLocaleString('en-IN')}</div>
                      <div className="text-sm text-gray-600">Cumulative Monthly Profit</div>
                    </div>
                    <div className="bg-yellow-50 rounded-lg p-4 text-center hover:bg-yellow-100 transition-colors border border-yellow-200">
                      <div className="text-2xl font-bold text-yellow-600">₹{(2 * selectedEmp.salary).toLocaleString('en-IN')}</div>
                      <div className="text-sm text-gray-600">Eligibility Threshold (2× Salary)</div>
                    </div>
                    <div className="bg-purple-50 rounded-lg p-4 text-center hover:bg-purple-100 transition-colors border border-purple-200">
                      <div className="text-2xl font-bold text-purple-600">₹{(12 * selectedEmp.salary).toLocaleString('en-IN')}</div>
                      <div className="text-sm text-gray-600">Annual Salary Threshold</div>
                    </div>
                  </div>

                  {/* Final Incentive Card */}
                  <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-6 border-2 border-purple-200">
                    <div className="flex justify-between items-start mb-4">
                      <h3 className="text-xl font-bold text-purple-800 flex items-center">
                        <Award className="w-5 h-5 mr-2" />
                        Final Monthly Incentive Calculation
                      </h3>
                      <div className={`px-3 py-1 rounded-full text-xs font-medium border ${getCaseColor(incentiveResult?.caseNumber)}`}>
                        {incentiveResult?.case?.split(':')[0]}
                      </div>
                    </div>
                    
                    <div className="text-4xl font-bold text-purple-600 mb-2">
                      ₹{incentiveResult?.incentive.toLocaleString('en-IN') || '0'}
                    </div>
                    
                    {incentiveResult?.capped && (
                      <div className="text-red-600 font-medium flex items-center mb-2">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        Capped at {incentiveResult?.maxCap === selectedEmp.salary ? 'monthly salary' : '2× monthly salary'} limit
                        (₹{incentiveResult?.maxCap.toLocaleString('en-IN')})
                      </div>
                    )}
                    
                    <div className="text-purple-700 text-sm font-medium mb-2">
                      {incentiveResult?.case}
                    </div>
                    
                    {!incentiveResult?.eligible && (
                      <div className="text-gray-600 text-sm bg-red-50 p-3 rounded border border-red-200">
                        <strong>Reason:</strong> {incentiveResult?.reason}
                      </div>
                    )}
                  </div>

                  {/* Detailed Calculation Breakdown */}
                  {incentiveResult && (
                    <div className="bg-gray-50 rounded-lg p-6">
                      <h4 className="font-bold text-gray-800 mb-4 flex items-center">
                        <Calculator className="w-5 h-5 mr-2" />
                        Detailed Incentive Calculation Breakdown
                      </h4>
                      
                      <div className="grid md:grid-cols-2 gap-6">
                        {/* Left Column - Basic Info */}
                        <div className="space-y-3">
                          <h5 className="font-semibold text-gray-700 mb-3">Employee & Threshold Information</h5>
                          <div className="flex justify-between items-center p-3 bg-white rounded border">
                            <span className="text-gray-700">Monthly Salary:</span>
                            <span className="font-semibold">₹{selectedEmp.salary.toLocaleString('en-IN')}</span>
                          </div>
                          <div className="flex justify-between items-center p-3 bg-white rounded border">
                            <span className="text-gray-700">Cumulative Monthly Profit:</span>
                            <span className="font-semibold">₹{monthlyProfit.toLocaleString('en-IN')}</span>
                          </div>
                          <div className="flex justify-between items-center p-3 bg-white rounded border">
                            <span className="text-gray-700">Eligibility Threshold (2× Salary):</span>
                            <span className="font-semibold">₹{incentiveResult.eligibilityThreshold.toLocaleString('en-IN')}</span>
                          </div>
                          <div className="flex justify-between items-center p-3 bg-white rounded border">
                            <span className="text-gray-700">Annual Salary Threshold:</span>
                            <span className="font-semibold">₹{incentiveResult.annualSalary.toLocaleString('en-IN')}</span>
                          </div>
                        </div>

                        {/* Right Column - Calculation */}
                        <div className="space-y-3">
                          <h5 className="font-semibold text-gray-700 mb-3">Incentive Calculation Details</h5>
                          {incentiveResult.eligible ? (
                            <>
                              <div className="flex justify-between items-center p-3 bg-blue-50 rounded border border-blue-200">
                                <span className="text-gray-700">Calculation Method:</span>
                                <span className="font-semibold text-blue-600">
                                  {monthlyProfit > incentiveResult.annualSalary 
                                    ? `₹${monthlyProfit.toLocaleString('en-IN')} × 15%`
                                    : `(₹${monthlyProfit.toLocaleString('en-IN')} - ₹${selectedEmp.salary.toLocaleString('en-IN')}) × 15%`
                                  }
                                </span>
                              </div>
                              <div className="flex justify-between items-center p-3 bg-white rounded border">
                                <span className="text-gray-700">Calculated Incentive:</span>
                                <span className="font-semibold">₹{incentiveResult.calculated.toLocaleString('en-IN')}</span>
                              </div>
                              <div className="flex justify-between items-center p-3 bg-white rounded border">
                                <span className="text-gray-700">Maximum Allowed Cap:</span>
                                <span className="font-semibold">₹{incentiveResult.maxCap.toLocaleString('en-IN')} ({incentiveResult.maxCap === selectedEmp.salary ? '1×' : '2×'} salary)</span>
                              </div>
                              <div className="flex justify-between items-center p-3 bg-green-50 rounded border-2 border-green-200">
                                <span className="text-gray-800 font-bold">Final Incentive:</span>
                                <span className="font-bold text-green-600 text-lg">
                                  ₹{incentiveResult.incentive.toLocaleString('en-IN')} 
                                  {incentiveResult.capped && <span className="text-red-600 ml-2 text-sm">(CAPPED)</span>}
                                </span>
                              </div>
                            </>
                          ) : (
                            <div className="p-3 bg-red-50 rounded border border-red-200">
                              <span className="text-red-700 font-medium">No incentive calculation - eligibility threshold not met</span>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Projects List */}
                  {empProjects.length > 0 && (
                    <div>
                      <h3 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                        <Target className="w-5 h-5 mr-2" />
                        Projects Completed in {selectedMonth}
                      </h3>
                      <div className="space-y-3">
                        {empProjects.map(project => (
                          <div key={project.id} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors border">
                            <div className="flex justify-between items-start">
                              <div className="flex-1">
                                <h4 className="font-bold text-gray-800 mb-2">{project.projectName}</h4>
                                <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                                  <div>
                                    <span className="font-medium">Buyer:</span> {project.buyerName}
                                  </div>
                                  <div>
                                    <span className="font-medium">Location:</span> {project.location}
                                  </div>
                                  <div>
                                    <span className="font-medium">Expected Cost:</span> ₹{project.expectedCost.toLocaleString('en-IN')}
                                  </div>
                                  <div>
                                    <span className="font-medium">Sale Value:</span> ₹{project.saleValue.toLocaleString('en-IN')}
                                  </div>
                                  <div>
                                    <span className="font-medium">Date:</span> {project.date}
                                  </div>
                                </div>
                              </div>
                              <div className="text-right ml-4">
                                <div className={`text-xl font-bold ${project.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  {project.profit >= 0 ? '+' : ''}₹{project.profit.toLocaleString('en-IN')}
                                </div>
                                <div className="text-sm text-gray-500">Project Profit</div>
                                <button
                                  onClick={() => deleteProject(project.id)}
                                  className="text-red-600 hover:text-red-800 p-1 rounded mt-2 hover:bg-red-100 transition-colors"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {!selectedEmployee && (
                <div className="text-center py-12 text-gray-500">
                  <Calculator className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <p className="text-lg">Select an employee to view their incentive dashboard</p>
                  <p className="text-sm mt-2">Add employees in the "Manage Employees" tab to get started</p>
                </div>
              )}
            </div>
          )}

          {activeTab === 'employees' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-800">Manage Employees</h2>
              
              <div className="bg-green-50 rounded-lg p-6">
                <h3 className="text-lg font-bold text-green-800 mb-4">Add New Employee</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Employee Name</label>
                    <input
                      type="text"
                      value={newEmployee.name}
                      onChange={(e) => setNewEmployee({...newEmployee, name: e.target.value})}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 transition-all"
                      placeholder="Enter employee name"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Monthly Salary (₹)</label>
                    <input
                      type="text"
                      value={newEmployee.salary}
                      onChange={(e) => handleNumberInput(e.target.value, setNewEmployee, 'salary')}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 transition-all"
                      placeholder="Enter monthly salary"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <button
                      onClick={addEmployee}
                      disabled={!newEmployee.name || !newEmployee.salary}
                      className="w-full bg-green-600 text-white p-3 rounded-lg hover:bg-green-700 transition-colors flex items-center justify-center disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Employee
                    </button>
                  </div>
                </div>
              </div>

              {/* Employee List */}
              <div>
                <h3 className="text-xl font-bold text-gray-800 mb-4">Current Employees</h3>
                <div className="grid gap-4">
                  {employees.map(emp => (
                    <div key={emp.id} className="bg-gray-50 rounded-lg p-4 flex justify-between items-center">
                      <div>
                        <h4 className="font-bold text-gray-800">{emp.name}</h4>
                        <p className="text-gray-600">Monthly Salary: ₹{emp.salary.toLocaleString('en-IN')}</p>
                        <p className="text-gray-600">Incentive Rate: {emp.incentiveRate}%</p>
                      </div>
                      <button
                        onClick={() => deleteEmployee(emp.id)}
                        className="text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-100 transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'projects' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-800">Add New Project</h2>
              
              <div className="bg-blue-50 rounded-lg p-6">
                <h3 className="text-lg font-bold text-blue-800 mb-4">Project Information</h3>
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Select Employee *</label>
                    <select
                      value={selectedEmployee}
                      onChange={(e) => setSelectedEmployee(e.target.value)}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                    >
                      <option value="">Select Employee</option>
                      {employees.map(emp => (
                        <option key={emp.id} value={emp.id}>{emp.name}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Project Name *</label>
                    <input
                      type="text"
                      value={newProject.projectName}
                      onChange={(e) => setNewProject({...newProject, projectName: e.target.value})}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                      placeholder="Enter project name"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Buyer Name</label>
                    <input
                      type="text"
                      value={newProject.buyerName}
                      onChange={(e) => setNewProject({...newProject, buyerName: e.target.value})}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                      placeholder="Enter buyer company name"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Project Location</label>
                    <input
                      type="text"
                      value={newProject.location}
                      onChange={(e) => setNewProjecProject({...newProject, location: e.target.value})}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                      placeholder="Enter project location"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Expected Cost (₹) *</label>
                    <input
                      type="text"
                      value={newProject.expectedCost}
                      onChange={(e) => handleNumberInput(e.target.value, setNewProject, 'expectedCost')}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                      placeholder="Enter expected project cost"
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">Sale Value (₹) *</label>
                    <input
                      type="text"
                      value={newProject.saleValue}
                      onChange={(e) => handleNumberInput(e.target.value, setNewProject, 'saleValue')}
                      className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 transition-all"
                      placeholder="Enter project sale value"
                    />
                  </div>

                  <div className="md:col-span-2">
                    {parseNumber(newProject.saleValue) && parseNumber(newProject.expectedCost) && (
                      <div className="mb-4 p-3 bg-white rounded-lg border">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-700">Calculated Profit:</span>
                          <span className={`font-bold text-lg ${
                            (parseNumber(newProject.saleValue) - parseNumber(newProject.expectedCost)) >= 0 
                              ? 'text-green-600' 
                              : 'text-red-600'
                          }`}>
                            ₹{(parseNumber(newProject.saleValue) - parseNumber(newProject.expectedCost)).toLocaleString('en-IN')}
                          </span>
                        </div>
                      </div>
                    )}
                    
                    <button
                      onClick={addProject}
                      disabled={!newProject.projectName || !newProject.expectedCost || !newProject.saleValue || !selectedEmployee}
                      className="w-full bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center disabled:bg-gray-400 disabled:cursor-not-allowed"
                    >
                      <Plus className="w-4 h-4 mr-2" />
                      Add Project
                    </button>
                  </div>
                </div>
              </div>

              {/* All Projects List */}
              <div>
                <h3 className="text-xl font-bold text-gray-800 mb-4">All Projects</h3>
                <div className="space-y-4">
                  {projects.map(project => {
                    const employee = employees.find(emp => emp.id === parseInt(project.employee));
                    return (
                      <div key={project.id} className="bg-gray-50 rounded-lg p-4 hover:bg-gray-100 transition-colors border">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <div className="flex items-center mb-2">
                              <h4 className="font-bold text-gray-800 mr-3">{project.projectName}</h4>
                              <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                {employee?.name}
                              </span>
                            </div>
                            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                              <div>
                                <span className="font-medium">Buyer:</span> {project.buyerName}
                              </div>
                              <div>
                                <span className="font-medium">Location:</span> {project.location}
                              </div>
                              <div>
                                <span className="font-medium">Expected Cost:</span> ₹{project.expectedCost.toLocaleString('en-IN')}
                              </div>
                              <div>
                                <span className="font-medium">Sale Value:</span> ₹{project.saleValue.toLocaleString('en-IN')}
                              </div>
                              <div>
                                <span className="font-medium">Date:</span> {project.date}
                              </div>
                            </div>
                          </div>
                          <div className="text-right ml-4">
                            <div className={`text-xl font-bold ${project.profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              {project.profit >= 0 ? '+' : ''}₹{project.profit.toLocaleString('en-IN')}
                            </div>
                            <div className="text-sm text-gray-500">Project Profit</div>
                            <button
                              onClick={() => deleteProject(project.id)}
                              className="text-red-600 hover:text-red-800 p-1 rounded mt-2 hover:bg-red-100 transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                  
                  {projects.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <TrendingUp className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                      <p>No projects added yet</p>
                      <p className="text-sm mt-1">Add your first project above to get started</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'rules' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">Company Incentive Policy Rules</h2>
              
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
                <div className="flex items-center mb-4">
                  <Info className="w-6 h-6 text-blue-600 mr-2" />
                  <h3 className="text-xl font-bold text-blue-800">Monthly Incentive Calculation Framework</h3>
                </div>
                <p className="text-blue-700 mb-4">
                  The incentive system is designed to reward employees based on their monthly performance and profit generation, 
                  with built-in thresholds and caps to ensure fair and sustainable compensation.
                </p>
              </div>

              <div className="grid gap-6">
                {/* Case 1 */}
                <div className="bg-red-50 rounded-lg p-6 border-l-4 border-red-500">
                  <h4 className="text-lg font-bold text-red-800 mb-3 flex items-center">
                    <AlertCircle className="w-5 h-5 mr-2" />
                    Case 1: Employee Not Yet Eligible for Incentive
                  </h4>
                  <div className="text-red-700 space-y-2">
                    <p><strong>Condition:</strong> Cumulative monthly profit &lt; 2× monthly salary</p>
                    <p><strong>Incentive:</strong> ₹0 (No incentive awarded)</p>
                    <p><strong>Reason:</strong> Employee needs to generate profit equivalent to at least 2× their monthly salary to become eligible for incentive payments.</p>
                  </div>
                  <div className="mt-3 p-3 bg-red-100 rounded text-sm text-red-600">
                    <strong>Example:</strong> If monthly salary is ₹50,000, employee must generate at least ₹1,00,000 in profit to qualify for incentive.
                  </div>
                </div>

                {/* Case 2 */}
                <div className="bg-green-50 rounded-lg p-6 border-l-4 border-green-500">
                  <h4 className="text-lg font-bold text-green-800 mb-3 flex items-center">
                    <Award className="w-5 h-5 mr-2" />
                    Case 2: Employee Becomes Eligible - Normal Incentive Calculation
                  </h4>
                  <div className="text-green-700 space-y-2">
                    <p><strong>Condition:</strong> 2× monthly salary ≤ cumulative monthly profit ≤ annual salary</p>
                    <p><strong>Formula:</strong> (Gross Profit - Monthly Salary) × 15%</p>
                    <p><strong>Cap:</strong> Maximum incentive = Monthly salary</p>
                    <p><strong>Logic:</strong> Employee earns incentive on profit exceeding their monthly salary contribution.</p>
                  </div>
                  <div className="mt-3 p-3 bg-green-100 rounded text-sm text-green-600">
                    <strong>Example:</strong> Salary ₹50,000, Profit ₹2,00,000 → (₹2,00,000 - ₹50,000) × 15% = ₹22,500
                  </div>
                </div>

                {/* Case 3 */}
                <div className="bg-yellow-50 rounded-lg p-6 border-l-4 border-yellow-500">
                  <h4 className="text-lg font-bold text-yellow-800 mb-3 flex items-center">
                    <AlertCircle className="w-5 h-5 mr-2" />
                    Case 3: Incentive Higher Than Monthly Salary (Capping Applied)
                  </h4>
                  <div className="text-yellow-700 space-y-2">
                    <p><strong>Condition:</strong> Calculated incentive &gt; monthly salary (but profit ≤ annual salary)</p>
                    <p><strong>Calculation:</strong> Same as Case 2, but result is capped</p>
                    <p><strong>Final Incentive:</strong> Monthly salary (capped amount)</p>
                    <p><strong>Purpose:</strong> Prevents incentive from exceeding base salary for moderate performance levels.</p>
                  </div>
                  <div className="mt-3 p-3 bg-yellow-100 rounded text-sm text-yellow-600">
                    <strong>Example:</strong> Salary ₹50,000, Calculated ₹75,000 → Final incentive capped at ₹50,000
                  </div>
                </div>

                {/* Case 4 */}
                <div className="bg-purple-50 rounded-lg p-6 border-l-4 border-purple-500">
                  <h4 className="text-lg font-bold text-purple-800 mb-3 flex items-center">
                    <Award className="w-5 h-5 mr-2" />
                    Case 4: Exceptional Performance - Profit Exceeds Annual Salary
                  </h4>
                  <div className="text-purple-700 space-y-2">
                    <p><strong>Condition:</strong> Cumulative monthly profit &gt; annual salary (12× monthly salary)</p>
                    <p><strong>Formula:</strong> Total Gross Profit × 15% (no salary deduction)</p>
                    <p><strong>Enhanced Cap:</strong> Maximum incentive = 2× monthly salary</p>
                    <p><strong>Recognition:</strong> Exceptional performance deserves higher rewards and increased cap.</p>
                  </div>
                  <div className="mt-3 p-3 bg-purple-100 rounded text-sm text-purple-600">
                    <strong>Example:</strong> Salary ₹50,000 (Annual ₹6,00,000), Profit ₹8,00,000 → ₹8,00,000 × 15% = ₹1,20,000, capped at ₹1,00,000
                  </div>
                </div>
              </div>

              {/* Key Parameters */}
              <div className="bg-gray-50 rounded-lg p-6">
                <h4 className="text-lg font-bold text-gray-800 mb-4 flex items-center">
                  <Calculator className="w-5 h-5 mr-2" />
                  Key Parameters & Constants
                </h4>
                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-white rounded border">
                      <span className="text-gray-700">Incentive Rate:</span>
                      <span className="font-semibold text-blue-600">15% (Fixed)</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-white rounded border">
                      <span className="text-gray-700">Eligibility Threshold:</span>
                      <span className="font-semibold text-green-600">2× Monthly Salary</span>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center p-3 bg-white rounded border">
                      <span className="text-gray-700">Exceptional Performance Threshold:</span>
                      <span className="font-semibold text-purple-600">12× Monthly Salary</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-white rounded border">
                      <span className="text-gray-700">Standard Cap / Enhanced Cap:</span>
                      <span className="font-semibold text-yellow-600">1× / 2× Monthly Salary</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Implementation Notes */}
              <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
                <h4 className="text-lg font-bold text-blue-800 mb-3">Implementation Notes</h4>
                <ul className="text-blue-700 space-y-2 list-disc list-inside">
                  <li>All calculations are based on cumulative monthly profit across all projects assigned to the employee</li>
                  <li>Profit is calculated as: Sale Value - Expected Cost for each project</li>
                  <li>Thresholds and caps are recalculated monthly based on current salary</li>
                  <li>The 15% incentive rate is a company-wide standard applied uniformly</li>
                  <li>Capping ensures incentive payments remain within sustainable limits</li>
                  <li>Exceptional performance recognition encourages high achievers while maintaining cost control</li>
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default IncentiveTracker;