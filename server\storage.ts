import { employees, projects, users, type Employee, type InsertEmployee, type Project, type InsertProject, type User, type InsertUser } from "@shared/schema";

export interface IStorage {
  // User methods (existing)
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Employee methods
  getAllEmployees(): Promise<Employee[]>;
  getEmployee(id: number): Promise<Employee | undefined>;
  createEmployee(employee: InsertEmployee): Promise<Employee>;
  deleteEmployee(id: number): Promise<void>;
  
  // Project methods
  getAllProjects(): Promise<Project[]>;
  getProjectsByEmployee(employeeId: number): Promise<Project[]>;
  createProject(project: InsertProject): Promise<Project>;
  deleteProject(id: number): Promise<void>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private employees: Map<number, Employee>;
  private projects: Map<number, Project>;
  private nextUserId: number;
  private nextEmployeeId: number;
  private nextProjectId: number;

  constructor() {
    this.users = new Map();
    this.employees = new Map();
    this.projects = new Map();
    this.nextUserId = 1;
    this.nextEmployeeId = 1;
    this.nextProjectId = 1;
  }

  // User methods (existing)
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.nextUserId++;
    const now = new Date();
    
    const user: User = { 
      ...insertUser, 
      id,
      createdAt: now,
      isActive: true
    };
    
    this.users.set(id, user);
    return user;
  }

  // Employee methods
  async getAllEmployees(): Promise<Employee[]> {
    return Array.from(this.employees.values());
  }

  async getEmployee(id: number): Promise<Employee | undefined> {
    return this.employees.get(id);
  }

  async createEmployee(insertEmployee: InsertEmployee): Promise<Employee> {
    const id = this.nextEmployeeId++;
    const employee: Employee = {
      ...insertEmployee,
      id,
      incentiveRate: insertEmployee.incentiveRate || "15",
      createdAt: new Date(),
    };
    this.employees.set(id, employee);
    return employee;
  }

  async deleteEmployee(id: number): Promise<void> {
    this.employees.delete(id);
    // Also delete all projects for this employee
    const projectsToDelete: number[] = [];
    this.projects.forEach((project, projectId) => {
      if (project.employeeId === id) {
        projectsToDelete.push(projectId);
      }
    });
    projectsToDelete.forEach(projectId => this.projects.delete(projectId));
  }

  // Project methods
  async getAllProjects(): Promise<Project[]> {
    return Array.from(this.projects.values());
  }

  async getProjectsByEmployee(employeeId: number): Promise<Project[]> {
    return Array.from(this.projects.values()).filter(
      (project) => project.employeeId === employeeId
    );
  }

  async createProject(insertProject: InsertProject): Promise<Project> {
    const id = this.nextProjectId++;
    const profit = insertProject.saleValue - insertProject.expectedCost;
    const project: Project = {
      ...insertProject,
      id,
      profit,
      date: new Date(),
    };
    this.projects.set(id, project);
    return project;
  }

  async deleteProject(id: number): Promise<void> {
    this.projects.delete(id);
  }

  async getUserByERPNextId(erpnextId: string): Promise<User | undefined> {
    const users = Array.from(this.users.values());
    return users.find(user => user.erpnextId === erpnextId);
  }

  async getProjectsByUserId(userId: number): Promise<Project[]> {
    const projects = Array.from(this.projects.values());
    return projects.filter(project => project.userId === userId);
  }
}

export const storage = new MemStorage();



