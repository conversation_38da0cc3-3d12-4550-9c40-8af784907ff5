import { QueryClient, QueryFunction } from "@tanstack/react-query";
import axios from 'axios';
import { getUserData } from './auth';

const ERPNEXT_API_URL = 'https://citysceneglobal.com';
const ERPNEXT_API_KEY = '43bf09851d9bf10';
const ERPNEXT_API_SECRET = '51b781bc020a670';
const AUTH_TOKEN = `token ${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`;

export async function apiRequest(
  method: string,
  endpoint: string,
  data?: unknown | undefined,
): Promise<any> {
  const url = `${ERPNEXT_API_URL}${endpoint}`;
  
  try {
    const response = await axios({
      method,
      url,
      data,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': AUTH_TOKEN
      }
    });
    
    return response.data;
  } catch (error) {
    console.error(`API request failed: ${method} ${endpoint}`, error);
    throw error;
  }
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    try {
      const [endpoint, params] = queryKey as [string, any];
      const url = `${ERPNEXT_API_URL}${endpoint}`;
      
      const response = await axios.get(url, {
        params,
        headers: {
          'Authorization': AUTH_TOKEN
        }
      });
      
      return response.data;
    } catch (error: any) {
      if (unauthorizedBehavior === "returnNull" && error.response?.status === 401) {
        return null;
      }
      throw error;
    }
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

