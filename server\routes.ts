import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { insertProjectSchema } from "@shared/schema";
import { isAuthenticated, hasRole, authenticateWithERPNext, isAdmin } from "./auth";
import axios from 'axios';

const ERPNEXT_API_URL = process.env.ERPNEXT_API_URL || 'https://citysceneglobal.com';
const ERPNEXT_API_KEY = process.env.ERPNEXT_API_KEY || '43bf09851d9bf10';
const ERPNEXT_API_SECRET = process.env.ERPNEXT_API_SECRET || '51b781bc020a670';

export async function registerRoutes(app: Express): Promise<Server> {
  // Authentication routes
  app.post("/api/login", async (req, res) => {
    try {
      const { username, password } = req.body;
      
      console.log(`Login attempt for user: ${username}`);
      
      // Authenticate with ERPNext
      const erpnextUser = await authenticateWithERPNext(username, password);
      if (!erpnextUser) {
        console.log(`Authentication failed for user: ${username}`);
        return res.status(401).json({ message: "Invalid credentials" });
      }
      
      console.log(`Authentication successful for user: ${username}`);
      
      // Check if user exists in our system
      let user = await storage.getUserByERPNextId(erpnextUser.name);
      
      // If not, create a new user
      if (!user) {
        console.log(`Creating new user for: ${erpnextUser.name}`);
        const newUser = {
          username: erpnextUser.name,
          password: "erpnext-auth", // Placeholder, not used for auth
          erpnextId: erpnextUser.name,
          email: erpnextUser.email || erpnextUser.name,
          role: erpnextUser.role_profile_name?.toLowerCase() || "employee",
          isAdmin: isAdmin(erpnextUser.email || erpnextUser.name)
        };
        
        user = await storage.createUser(newUser);
      }
      
      // Set session
      req.session.userId = user.id;
      req.session.erpnextToken = `${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`;
      
      console.log(`Session created for user: ${user.username}`);
      
      res.json({ 
        message: "Login successful", 
        user: { 
          id: user.id, 
          username: user.username, 
          role: user.role,
          email: user.email,
          isAdmin: isAdmin(user.email || '')
        } 
      });
    } catch (error) {
      console.error("Login error:", error);
      res.status(500).json({ message: "Login failed" });
    }
  });

  app.post("/api/logout", (req, res) => {
    req.session.destroy((err) => {
      if (err) {
        return res.status(500).json({ message: "Logout failed" });
      }
      res.json({ message: "Logged out successfully" });
    });
  });

  app.get("/api/me", isAuthenticated, async (req, res) => {
    try {
      const user = await storage.getUser(req.session.userId);
      if (!user) {
        return res.status(404).json({ message: "User not found" });
      }
      
      res.json({
        id: user.id,
        username: user.username,
        role: user.role,
        email: user.email,
        isAdmin: isAdmin(user.email || '')
      });
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch user data" });
    }
  });

  // Employee routes
  app.get("/api/employees", isAuthenticated, async (req, res) => {
    try {
      const token = `${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`;
      
      const response = await axios.get(`${ERPNEXT_API_URL}/api/resource/Employee`, {
        params: {
          fields: JSON.stringify(["name", "employee_name", "company_email", "personal_email"])
        },
        headers: {
          'Authorization': `token ${token}`
        }
      });
      
      const employees = response.data.data.map(emp => ({
        id: emp.name,
        name: emp.employee_name,
        email: emp.company_email || emp.personal_email,
        incentiveRate: "15" // Default incentive rate as per requirements
      }));
      
      res.json(employees);
    } catch (error) {
      console.error("Error fetching employees:", error);
      res.status(500).json({ message: "Failed to fetch employees" });
    }
  });

  // Project routes
  app.get("/api/projects", isAuthenticated, async (req, res) => {
    try {
      const user = await storage.getUser(req.session.userId);
      
      if (!user) {
        return res.status(401).json({ message: "User not found" });
      }
      
      const token = `${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`;
      let filter = [];
      
      // If not admin, filter by user
      if (!isAdmin(user.email || '')) {
        filter.push(["assigned_to", "=", user.erpnextId]);
      }
      
      // Apply date filters if provided
      if (req.query.period) {
        const today = new Date();
        let startDate = new Date();
        
        if (req.query.period === 'week') {
          startDate.setDate(today.getDate() - 7);
        } else if (req.query.period === 'month') {
          startDate.setMonth(today.getMonth() - 1);
        }
        
        filter.push(["creation", ">=", startDate.toISOString().split('T')[0]]);
      }
      
      const response = await axios.get(`${ERPNEXT_API_URL}/api/resource/KPI Sales`, {
        params: {
          fields: JSON.stringify([
            "name", "project_name", "expected_cost", "sale_value", 
            "assigned_to", "creation", "buyer_name", "location"
          ]),
          filters: JSON.stringify(filter)
        },
        headers: {
          'Authorization': `token ${token}`
        }
      });
      
      const projects = response.data.data.map(proj => ({
        id: proj.name,
        projectName: proj.project_name,
        expectedCost: proj.expected_cost,
        saleValue: proj.sale_value,
        profit: proj.sale_value - proj.expected_cost,
        employeeId: proj.assigned_to,
        date: new Date(proj.creation).toISOString(),
        buyerName: proj.buyer_name || 'N/A',
        location: proj.location || 'N/A'
      }));
      
      res.json(projects);
    } catch (error) {
      console.error("Error fetching projects:", error);
      res.status(500).json({ message: "Failed to fetch projects" });
    }
  });

  app.get("/api/projects/employee/:employeeId", isAuthenticated, async (req, res) => {
    try {
      const employeeId = req.params.employeeId;
      const token = `${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`;
      
      const response = await axios.get(`${ERPNEXT_API_URL}/api/resource/KPI Sales`, {
        params: {
          fields: JSON.stringify([
            "name", "project_name", "expected_cost", "sale_value", 
            "assigned_to", "creation", "buyer_name", "location"
          ]),
          filters: JSON.stringify([["assigned_to", "=", employeeId]])
        },
        headers: {
          'Authorization': `token ${token}`
        }
      });
      
      const projects = response.data.data.map(proj => ({
        id: proj.name,
        projectName: proj.project_name,
        expectedCost: proj.expected_cost,
        saleValue: proj.sale_value,
        profit: proj.sale_value - proj.expected_cost,
        employeeId: proj.assigned_to,
        date: new Date(proj.creation).toISOString(),
        buyerName: proj.buyer_name || 'N/A',
        location: proj.location || 'N/A'
      }));
      
      res.json(projects);
    } catch (error) {
      console.error("Error fetching employee projects:", error);
      res.status(500).json({ message: "Failed to fetch employee projects" });
    }
  });

  app.post("/api/projects", isAuthenticated, async (req, res) => {
    try {
      const validatedData = insertProjectSchema.parse(req.body);
      const token = `${ERPNEXT_API_KEY}:${ERPNEXT_API_SECRET}`;
      
      // Prepare data for ERPNext
      const projectData = {
        doctype: "KPI Sales",
        project_name: validatedData.projectName,
        expected_cost: validatedData.expectedCost,
        sale_value: validatedData.saleValue,
        assigned_to: validatedData.employeeId,
        buyer_name: validatedData.buyerName || '',
        location: validatedData.location || ''
      };
      
      // Create project in ERPNext
      const response = await axios.post(
        `${ERPNEXT_API_URL}/api/resource/KPI Sales`,
        projectData,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `token ${token}`
          }
        }
      );
      
      const createdProject = {
        id: response.data.data.name,
        ...validatedData,
        profit: validatedData.saleValue - validatedData.expectedCost,
        date: new Date().toISOString()
      };
      
      res.status(201).json(createdProject);
    } catch (error) {
      console.error("Error creating project:", error);
      res.status(400).json({ message: "Invalid project data" });
    }
  });

  // Create HTTP server
  const server = createServer(app);
  return server;
}



